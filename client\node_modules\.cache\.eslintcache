[{"C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\UserContext.js": "4", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Dashboard\\UserDashboard.js": "5", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\Leaderboard.js": "6", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\Login.js": "7", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BoardViewer.js": "8", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\HowToPlay\\HowToPlay.js": "9", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Cards\\Cards.js": "10", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoBoard.js": "11", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PortraitOverlay.js": "12", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoSquare.js": "13", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\ConfirmationModal.js": "14", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PointsDisplay.js": "15", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Admin\\AdminPanel.js": "16"}, {"size": 535, "mtime": 1747521110957, "results": "17", "hashOfConfig": "18"}, {"size": 6318, "mtime": 1749966420700, "results": "19", "hashOfConfig": "18"}, {"size": 362, "mtime": 1747521110970, "results": "20", "hashOfConfig": "18"}, {"size": 4402, "mtime": 1749966371303, "results": "21", "hashOfConfig": "18"}, {"size": 9753, "mtime": 1749093515268, "results": "22", "hashOfConfig": "18"}, {"size": 3321, "mtime": 1750017531057, "results": "23", "hashOfConfig": "18"}, {"size": 4286, "mtime": 1749966386862, "results": "24", "hashOfConfig": "18"}, {"size": 3819, "mtime": 1749093515247, "results": "25", "hashOfConfig": "18"}, {"size": 6251, "mtime": 1749285855260, "results": "26", "hashOfConfig": "18"}, {"size": 4787, "mtime": 1749449451706, "results": "27", "hashOfConfig": "18"}, {"size": 10209, "mtime": 1749857737465, "results": "28", "hashOfConfig": "18"}, {"size": 11191, "mtime": 1749962472555, "results": "29", "hashOfConfig": "18"}, {"size": 4347, "mtime": 1749448657676, "results": "30", "hashOfConfig": "18"}, {"size": 745, "mtime": 1747885973100, "results": "31", "hashOfConfig": "18"}, {"size": 5915, "mtime": 1749963470027, "results": "32", "hashOfConfig": "18"}, {"size": 16486, "mtime": 1749966821357, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10mvb5b", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\UserContext.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Dashboard\\UserDashboard.js", ["82", "83", "84"], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\Leaderboard.js", ["85"], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\Login.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BoardViewer.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\HowToPlay\\HowToPlay.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Cards\\Cards.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoBoard.js", ["86"], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PortraitOverlay.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoSquare.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\ConfirmationModal.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PointsDisplay.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Admin\\AdminPanel.js", ["87", "88", "89", "90", "91", "92", "93", "94"], [], {"ruleId": "95", "severity": 1, "message": "96", "line": 63, "column": 6, "nodeType": "97", "endLine": 63, "endColumn": 12, "suggestions": "98"}, {"ruleId": "99", "severity": 1, "message": "100", "line": 72, "column": 9, "nodeType": "101", "messageId": "102", "endLine": 72, "endColumn": 20}, {"ruleId": "99", "severity": 1, "message": "103", "line": 134, "column": 9, "nodeType": "101", "messageId": "102", "endLine": 134, "endColumn": 27}, {"ruleId": "99", "severity": 1, "message": "104", "line": 45, "column": 9, "nodeType": "101", "messageId": "102", "endLine": 45, "endColumn": 17}, {"ruleId": "95", "severity": 1, "message": "105", "line": 80, "column": 6, "nodeType": "97", "endLine": 80, "endColumn": 72, "suggestions": "106"}, {"ruleId": "99", "severity": 1, "message": "107", "line": 16, "column": 10, "nodeType": "101", "messageId": "102", "endLine": 16, "endColumn": 24}, {"ruleId": "99", "severity": 1, "message": "108", "line": 16, "column": 26, "nodeType": "101", "messageId": "102", "endLine": 16, "endColumn": 43}, {"ruleId": "99", "severity": 1, "message": "109", "line": 17, "column": 10, "nodeType": "101", "messageId": "102", "endLine": 17, "endColumn": 23}, {"ruleId": "99", "severity": 1, "message": "110", "line": 17, "column": 25, "nodeType": "101", "messageId": "102", "endLine": 17, "endColumn": 41}, {"ruleId": "99", "severity": 1, "message": "111", "line": 21, "column": 10, "nodeType": "101", "messageId": "102", "endLine": 21, "endColumn": 23}, {"ruleId": "99", "severity": 1, "message": "112", "line": 21, "column": 25, "nodeType": "101", "messageId": "102", "endLine": 21, "endColumn": 41}, {"ruleId": "113", "severity": 2, "message": "114", "line": 29, "column": 3, "nodeType": "101", "endLine": 29, "endColumn": 12}, {"ruleId": "95", "severity": 1, "message": "115", "line": 392, "column": 6, "nodeType": "97", "endLine": 392, "endColumn": 13, "suggestions": "116"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'navigate'. Either include it or remove the dependency array.", "ArrayExpression", ["117"], "no-unused-vars", "'viewMyBoard' is assigned a value but never used.", "Identifier", "unusedVar", "'handleRefreshClick' is assigned a value but never used.", "'maxScore' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'calculateTotalPoints'. Either include it or remove the dependency array.", ["118"], "'selectedPlayer' is assigned a value but never used.", "'setSelectedPlayer' is assigned a value but never used.", "'selectedBoard' is assigned a value but never used.", "'setSelectedBoard' is assigned a value but never used.", "'boardProgress' is assigned a value but never used.", "'setBoardProgress' is assigned a value but never used.", "react-hooks/rules-of-hooks", "React Hook \"useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "React Hook useEffect has a missing dependency: 'fetchBoardProgress'. Either include it or remove the dependency array.", ["119"], {"desc": "120", "fix": "121"}, {"desc": "122", "fix": "123"}, {"desc": "124", "fix": "125"}, "Update the dependencies array to be: [navigate, user]", {"range": "126", "text": "127"}, "Update the dependencies array to be: [markedCells, userImages, isReadOnly, userId, boardId, characters, calculateTotalPoints]", {"range": "128", "text": "129"}, "Update the dependencies array to be: [board, fetchBoardProgress]", {"range": "130", "text": "131"}, [2352, 2358], "[navigate, user]", [2821, 2887], "[markedCells, userImages, isReadOnly, userId, boardId, characters, calculateTotalPoints]", [12207, 12214], "[board, fetchBoardProgress]"]