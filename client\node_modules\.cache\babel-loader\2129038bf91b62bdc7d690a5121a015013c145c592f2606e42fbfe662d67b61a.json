{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{useUser}from'../Auth/UserContext';import{useNavigate}from'react-router-dom';import'./AdminPanel.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminPanel=()=>{const{user,logout}=useUser();const navigate=useNavigate();const[loading,setLoading]=useState(false);const[message,setMessage]=useState('');const[messageType,setMessageType]=useState('');// 'success' or 'error'\n// State for different admin operations\nconst[players,setPlayers]=useState([]);const[boards,setBoards]=useState([]);const[selectedPlayer,setSelectedPlayer]=useState(null);const[selectedBoard,setSelectedBoard]=useState(null);const[editingDisplayName,setEditingDisplayName]=useState(null);const[newDisplayName,setNewDisplayName]=useState('');const[editingBoard,setEditingBoard]=useState(null);const[boardProgress,setBoardProgress]=useState(null);// Check if user is admin\nif(!user||!user.is_admin){return/*#__PURE__*/_jsx(\"div\",{className:\"admin-error\",children:\"Access denied. Admin privileges required.\"});}// Fetch players and boards on component mount\nuseEffect(()=>{fetchPlayers();fetchBoards();},[]);const showMessage=function(msg){let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'success';setMessage(msg);setMessageType(type);setTimeout(()=>{setMessage('');setMessageType('');},5000);};const fetchPlayers=async()=>{try{const response=await fetch('http://localhost:5000/api/users');if(response.ok){const data=await response.json();setPlayers(data);}}catch(error){console.error('Error fetching players:',error);}};const fetchBoards=async()=>{try{const response=await fetch('http://localhost:5000/api/admin/boards');if(response.ok){const data=await response.json();setBoards(data);}}catch(error){console.error('Error fetching boards:',error);}};const restartServer=async()=>{if(!window.confirm('Are you sure you want to restart the server? This will disconnect all users.')){return;}setLoading(true);try{const response=await fetch('http://localhost:5000/api/admin/restart-server',{method:'POST'});if(response.ok){showMessage('Server restart initiated. Please wait for reconnection.');}else{showMessage('Failed to restart server','error');}}catch(error){showMessage('Error restarting server','error');}setLoading(false);};const deleteAllBoards=async()=>{if(!window.confirm('Are you sure you want to delete ALL boards? This action cannot be undone.')){return;}setLoading(true);try{const response=await fetch('http://localhost:5000/api/admin/delete-all-boards',{method:'DELETE'});if(response.ok){const data=await response.json();showMessage(data.message);fetchBoards();// Refresh boards list\n}else{showMessage('Failed to delete all boards','error');}}catch(error){showMessage('Error deleting all boards','error');}setLoading(false);};const deleteAllPlayers=async()=>{if(!window.confirm('Are you sure you want to delete ALL players? This action cannot be undone.')){return;}setLoading(true);try{const response=await fetch('http://localhost:5000/api/admin/delete-all-players',{method:'DELETE'});if(response.ok){const data=await response.json();showMessage(data.message);fetchPlayers();// Refresh players list\nfetchBoards();// Refresh boards list\n}else{showMessage('Failed to delete all players','error');}}catch(error){showMessage('Error deleting all players','error');}setLoading(false);};const deletePlayer=async(playerId,playerName)=>{if(!window.confirm(\"Are you sure you want to delete player \\\"\".concat(playerName,\"\\\"? This action cannot be undone.\"))){return;}setLoading(true);try{const response=await fetch(\"http://localhost:5000/api/admin/delete-player/\".concat(playerId),{method:'DELETE'});if(response.ok){const data=await response.json();showMessage(data.message);fetchPlayers();// Refresh players list\nfetchBoards();// Refresh boards list\n}else{showMessage('Failed to delete player','error');}}catch(error){showMessage('Error deleting player','error');}setLoading(false);};const deleteBoard=async(userId,playerName)=>{if(!window.confirm(\"Are you sure you want to delete the board for \\\"\".concat(playerName,\"\\\"? This action cannot be undone.\"))){return;}setLoading(true);try{const response=await fetch(\"http://localhost:5000/api/admin/delete-board/\".concat(userId),{method:'DELETE'});if(response.ok){const data=await response.json();showMessage(data.message);fetchBoards();// Refresh boards list\n}else{showMessage('Failed to delete board','error');}}catch(error){showMessage('Error deleting board','error');}setLoading(false);};const updateDisplayName=async(userId,currentName)=>{if(!newDisplayName.trim()){showMessage('Please enter a new display name','error');return;}setLoading(true);try{const response=await fetch(\"http://localhost:5000/api/admin/update-display-name/\".concat(userId),{method:'PUT',headers:{'Content-Type':'application/json'},body:JSON.stringify({display_name:newDisplayName})});if(response.ok){const data=await response.json();showMessage(data.message);fetchPlayers();// Refresh players list\nfetchBoards();// Refresh boards list\nsetEditingDisplayName(null);setNewDisplayName('');}else{const data=await response.json();showMessage(data.error||'Failed to update display name','error');}}catch(error){showMessage('Error updating display name','error');}setLoading(false);};return/*#__PURE__*/_jsxs(\"div\",{className:\"admin-panel\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"admin-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Admin Panel\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-header-info\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"Welcome, \",user.display_name]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{logout();navigate('/');},className:\"admin-button secondary small\",children:\"Logout\"})]})]}),message&&/*#__PURE__*/_jsx(\"div\",{className:\"admin-message \".concat(messageType),children:message}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-sections\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"admin-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Server Management\"}),/*#__PURE__*/_jsx(\"button\",{onClick:restartServer,disabled:loading,className:\"admin-button danger\",children:\"Restart Server\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Bulk Operations\"}),/*#__PURE__*/_jsx(\"button\",{onClick:deleteAllBoards,disabled:loading,className:\"admin-button danger\",children:\"Delete All Boards\"}),/*#__PURE__*/_jsx(\"button\",{onClick:deleteAllPlayers,disabled:loading,className:\"admin-button danger\",children:\"Delete All Players\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Player Management\"}),/*#__PURE__*/_jsx(\"div\",{className:\"player-list\",children:players.map(player=>/*#__PURE__*/_jsxs(\"div\",{className:\"player-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"player-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"player-name\",children:player.display_name}),/*#__PURE__*/_jsxs(\"span\",{className:\"player-pin\",children:[\"PIN: \",player.pin]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"player-actions\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>deletePlayer(player.id,player.display_name),disabled:loading,className:\"admin-button small danger\",children:\"Delete Player\"}),editingDisplayName===player.id?/*#__PURE__*/_jsxs(\"div\",{className:\"edit-name-form\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:newDisplayName,onChange:e=>setNewDisplayName(e.target.value),placeholder:\"New display name\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>updateDisplayName(player.id,player.display_name),disabled:loading,className:\"admin-button small\",children:\"Save\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setEditingDisplayName(null);setNewDisplayName('');},className:\"admin-button small secondary\",children:\"Cancel\"})]}):/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setEditingDisplayName(player.id);setNewDisplayName(player.display_name);},disabled:loading,className:\"admin-button small\",children:\"Edit Name\"})]})]},player.id))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Board Management\"}),/*#__PURE__*/_jsx(\"div\",{className:\"board-list\",children:boards.map(board=>/*#__PURE__*/_jsxs(\"div\",{className:\"board-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"board-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"board-player\",children:board.display_name}),/*#__PURE__*/_jsxs(\"span\",{className:\"board-created\",children:[\"Created: \",new Date(board.created_at).toLocaleDateString()]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"board-actions\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>deleteBoard(board.user_id,board.display_name),disabled:loading,className:\"admin-button small danger\",children:\"Delete Board\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setEditingBoard(board),disabled:loading,className:\"admin-button small\",children:\"Edit Board\"})]})]},board.id))})]})]}),editingBoard&&/*#__PURE__*/_jsx(BoardEditor,{board:editingBoard,onClose:()=>setEditingBoard(null),onUpdate:()=>{fetchBoards();setEditingBoard(null);showMessage('Board updated successfully');}})]});};// Board Editor Component\nconst BoardEditor=_ref=>{let{board,onClose,onUpdate}=_ref;const[boardProgress,setBoardProgress]=useState(null);const[loading,setLoading]=useState(false);useEffect(()=>{fetchBoardProgress();},[board]);const fetchBoardProgress=async()=>{try{const response=await fetch(\"http://localhost:5000/api/boards/\".concat(encodeURIComponent(board.display_name),\"/progress\"));if(response.ok){const data=await response.json();setBoardProgress(data);}}catch(error){console.error('Error fetching board progress:',error);}};const toggleSquare=async squareIndex=>{if(!boardProgress)return;setLoading(true);try{const markedCells=[...boardProgress.marked_cells];const userImages=_objectSpread({},boardProgress.user_images);if(markedCells.includes(squareIndex)){// Unclaim the square\nconst index=markedCells.indexOf(squareIndex);markedCells.splice(index,1);delete userImages[squareIndex];}else{// Claim the square\nmarkedCells.push(squareIndex);}// Calculate new score\nlet newScore=0;markedCells.forEach(cellIndex=>{const character=board.board_data[cellIndex];if(character){switch(character.rarity){case'FREE':newScore+=1;break;case'R':newScore+=2;break;case'SR':newScore+=3;break;case'SSR':newScore+=4;break;case'UR+':newScore+=6;break;default:newScore+=1;break;}}});// Check for completed rows/columns and add bonus points\nconst completedLines=checkCompletedLines(markedCells);newScore+=completedLines*5;const response=await fetch(\"http://localhost:5000/api/admin/boards/\".concat(board.user_id,\"/progress\"),{method:'PUT',headers:{'Content-Type':'application/json'},body:JSON.stringify({marked_cells:markedCells,user_images:userImages,score:newScore})});if(response.ok){const updatedProgress=await response.json();setBoardProgress(updatedProgress);}}catch(error){console.error('Error updating board progress:',error);}setLoading(false);};const checkCompletedLines=markedCells=>{let completedLines=0;// Check rows\nfor(let row=0;row<5;row++){let rowComplete=true;for(let col=0;col<5;col++){if(!markedCells.includes(row*5+col)){rowComplete=false;break;}}if(rowComplete)completedLines++;}// Check columns\nfor(let col=0;col<5;col++){let colComplete=true;for(let row=0;row<5;row++){if(!markedCells.includes(row*5+col)){colComplete=false;break;}}if(colComplete)completedLines++;}return completedLines;};if(!boardProgress){return/*#__PURE__*/_jsx(\"div\",{className:\"board-editor-overlay\",children:/*#__PURE__*/_jsx(\"div\",{className:\"board-editor\",children:/*#__PURE__*/_jsx(\"div\",{children:\"Loading board...\"})})});}return/*#__PURE__*/_jsx(\"div\",{className:\"board-editor-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"board-editor\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"board-editor-header\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[\"Editing Board for \",board.display_name]}),/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"admin-button secondary\",children:\"Close\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"board-editor-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"board-grid\",children:board.board_data.map((character,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"board-square \".concat(boardProgress.marked_cells.includes(index)?'claimed':'',\" \").concat(character.rarity==='FREE'?'free':''),onClick:()=>toggleSquare(index),style:{cursor:loading?'not-allowed':'pointer'},children:character.rarity==='FREE'?'FREE':character.name},index))}),/*#__PURE__*/_jsxs(\"div\",{className:\"board-stats\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"Score: \",boardProgress.score]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Claimed Squares: \",boardProgress.marked_cells.length,\"/25\"]})]})]})]})});};export default AdminPanel;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useUser", "useNavigate", "jsx", "_jsx", "jsxs", "_jsxs", "AdminPanel", "user", "logout", "navigate", "loading", "setLoading", "message", "setMessage", "messageType", "setMessageType", "players", "setPlayers", "boards", "setBoards", "selectedPlayer", "setSelectedPlayer", "selectedBoard", "setSelectedBoard", "editingDisplayName", "setEditingDisplayName", "newDisplayName", "setNewDisplayName", "editingBoard", "setEditingBoard", "boardProgress", "setBoardProgress", "is_admin", "className", "children", "fetchPlayers", "fetchBoards", "showMessage", "msg", "type", "arguments", "length", "undefined", "setTimeout", "response", "fetch", "ok", "data", "json", "error", "console", "restartServer", "window", "confirm", "method", "deleteAllBoards", "deleteAllPlayers", "deletePlayer", "playerId", "<PERSON><PERSON><PERSON>", "concat", "deleteBoard", "userId", "updateDisplayName", "currentName", "trim", "headers", "body", "JSON", "stringify", "display_name", "onClick", "disabled", "map", "player", "pin", "id", "value", "onChange", "e", "target", "placeholder", "board", "Date", "created_at", "toLocaleDateString", "user_id", "BoardEditor", "onClose", "onUpdate", "_ref", "fetchBoardProgress", "encodeURIComponent", "toggleSquare", "squareIndex", "<PERSON><PERSON><PERSON><PERSON>", "marked_cells", "userImages", "_objectSpread", "user_images", "includes", "index", "indexOf", "splice", "push", "newScore", "for<PERSON>ach", "cellIndex", "character", "board_data", "rarity", "completedLines", "checkCompletedLines", "score", "updatedProgress", "row", "rowComplete", "col", "colComplete", "style", "cursor", "name"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Admin/AdminPanel.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useUser } from '../Auth/UserContext';\nimport { useNavigate } from 'react-router-dom';\nimport './AdminPanel.css';\n\nconst AdminPanel = () => {\n  const { user, logout } = useUser();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [messageType, setMessageType] = useState(''); // 'success' or 'error'\n  \n  // State for different admin operations\n  const [players, setPlayers] = useState([]);\n  const [boards, setBoards] = useState([]);\n  const [selectedPlayer, setSelectedPlayer] = useState(null);\n  const [selectedBoard, setSelectedBoard] = useState(null);\n  const [editingDisplayName, setEditingDisplayName] = useState(null);\n  const [newDisplayName, setNewDisplayName] = useState('');\n  const [editingBoard, setEditingBoard] = useState(null);\n  const [boardProgress, setBoardProgress] = useState(null);\n\n  // Check if user is admin\n  if (!user || !user.is_admin) {\n    return <div className=\"admin-error\">Access denied. Admin privileges required.</div>;\n  }\n\n  // Fetch players and boards on component mount\n  useEffect(() => {\n    fetchPlayers();\n    fetchBoards();\n  }, []);\n\n  const showMessage = (msg, type = 'success') => {\n    setMessage(msg);\n    setMessageType(type);\n    setTimeout(() => {\n      setMessage('');\n      setMessageType('');\n    }, 5000);\n  };\n\n  const fetchPlayers = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/users');\n      if (response.ok) {\n        const data = await response.json();\n        setPlayers(data);\n      }\n    } catch (error) {\n      console.error('Error fetching players:', error);\n    }\n  };\n\n  const fetchBoards = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/admin/boards');\n      if (response.ok) {\n        const data = await response.json();\n        setBoards(data);\n      }\n    } catch (error) {\n      console.error('Error fetching boards:', error);\n    }\n  };\n\n  const restartServer = async () => {\n    if (!window.confirm('Are you sure you want to restart the server? This will disconnect all users.')) {\n      return;\n    }\n    \n    setLoading(true);\n    try {\n      const response = await fetch('http://localhost:5000/api/admin/restart-server', {\n        method: 'POST',\n      });\n      \n      if (response.ok) {\n        showMessage('Server restart initiated. Please wait for reconnection.');\n      } else {\n        showMessage('Failed to restart server', 'error');\n      }\n    } catch (error) {\n      showMessage('Error restarting server', 'error');\n    }\n    setLoading(false);\n  };\n\n  const deleteAllBoards = async () => {\n    if (!window.confirm('Are you sure you want to delete ALL boards? This action cannot be undone.')) {\n      return;\n    }\n    \n    setLoading(true);\n    try {\n      const response = await fetch('http://localhost:5000/api/admin/delete-all-boards', {\n        method: 'DELETE',\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        showMessage(data.message);\n        fetchBoards(); // Refresh boards list\n      } else {\n        showMessage('Failed to delete all boards', 'error');\n      }\n    } catch (error) {\n      showMessage('Error deleting all boards', 'error');\n    }\n    setLoading(false);\n  };\n\n  const deleteAllPlayers = async () => {\n    if (!window.confirm('Are you sure you want to delete ALL players? This action cannot be undone.')) {\n      return;\n    }\n    \n    setLoading(true);\n    try {\n      const response = await fetch('http://localhost:5000/api/admin/delete-all-players', {\n        method: 'DELETE',\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        showMessage(data.message);\n        fetchPlayers(); // Refresh players list\n        fetchBoards(); // Refresh boards list\n      } else {\n        showMessage('Failed to delete all players', 'error');\n      }\n    } catch (error) {\n      showMessage('Error deleting all players', 'error');\n    }\n    setLoading(false);\n  };\n\n  const deletePlayer = async (playerId, playerName) => {\n    if (!window.confirm(`Are you sure you want to delete player \"${playerName}\"? This action cannot be undone.`)) {\n      return;\n    }\n    \n    setLoading(true);\n    try {\n      const response = await fetch(`http://localhost:5000/api/admin/delete-player/${playerId}`, {\n        method: 'DELETE',\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        showMessage(data.message);\n        fetchPlayers(); // Refresh players list\n        fetchBoards(); // Refresh boards list\n      } else {\n        showMessage('Failed to delete player', 'error');\n      }\n    } catch (error) {\n      showMessage('Error deleting player', 'error');\n    }\n    setLoading(false);\n  };\n\n  const deleteBoard = async (userId, playerName) => {\n    if (!window.confirm(`Are you sure you want to delete the board for \"${playerName}\"? This action cannot be undone.`)) {\n      return;\n    }\n    \n    setLoading(true);\n    try {\n      const response = await fetch(`http://localhost:5000/api/admin/delete-board/${userId}`, {\n        method: 'DELETE',\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        showMessage(data.message);\n        fetchBoards(); // Refresh boards list\n      } else {\n        showMessage('Failed to delete board', 'error');\n      }\n    } catch (error) {\n      showMessage('Error deleting board', 'error');\n    }\n    setLoading(false);\n  };\n\n  const updateDisplayName = async (userId, currentName) => {\n    if (!newDisplayName.trim()) {\n      showMessage('Please enter a new display name', 'error');\n      return;\n    }\n    \n    setLoading(true);\n    try {\n      const response = await fetch(`http://localhost:5000/api/admin/update-display-name/${userId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ display_name: newDisplayName }),\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        showMessage(data.message);\n        fetchPlayers(); // Refresh players list\n        fetchBoards(); // Refresh boards list\n        setEditingDisplayName(null);\n        setNewDisplayName('');\n      } else {\n        const data = await response.json();\n        showMessage(data.error || 'Failed to update display name', 'error');\n      }\n    } catch (error) {\n      showMessage('Error updating display name', 'error');\n    }\n    setLoading(false);\n  };\n\n  return (\n    <div className=\"admin-panel\">\n      <div className=\"admin-header\">\n        <h1>Admin Panel</h1>\n        <div className=\"admin-header-info\">\n          <p>Welcome, {user.display_name}</p>\n          <button\n            onClick={() => {\n              logout();\n              navigate('/');\n            }}\n            className=\"admin-button secondary small\"\n          >\n            Logout\n          </button>\n        </div>\n      </div>\n\n      {message && (\n        <div className={`admin-message ${messageType}`}>\n          {message}\n        </div>\n      )}\n\n      <div className=\"admin-sections\">\n        {/* Server Management */}\n        <div className=\"admin-section\">\n          <h2>Server Management</h2>\n          <button \n            onClick={restartServer} \n            disabled={loading}\n            className=\"admin-button danger\"\n          >\n            Restart Server\n          </button>\n        </div>\n\n        {/* Bulk Operations */}\n        <div className=\"admin-section\">\n          <h2>Bulk Operations</h2>\n          <button \n            onClick={deleteAllBoards} \n            disabled={loading}\n            className=\"admin-button danger\"\n          >\n            Delete All Boards\n          </button>\n          <button \n            onClick={deleteAllPlayers} \n            disabled={loading}\n            className=\"admin-button danger\"\n          >\n            Delete All Players\n          </button>\n        </div>\n\n        {/* Player Management */}\n        <div className=\"admin-section\">\n          <h2>Player Management</h2>\n          <div className=\"player-list\">\n            {players.map(player => (\n              <div key={player.id} className=\"player-item\">\n                <div className=\"player-info\">\n                  <span className=\"player-name\">{player.display_name}</span>\n                  <span className=\"player-pin\">PIN: {player.pin}</span>\n                </div>\n                <div className=\"player-actions\">\n                  <button\n                    onClick={() => deletePlayer(player.id, player.display_name)}\n                    disabled={loading}\n                    className=\"admin-button small danger\"\n                  >\n                    Delete Player\n                  </button>\n                  {editingDisplayName === player.id ? (\n                    <div className=\"edit-name-form\">\n                      <input\n                        type=\"text\"\n                        value={newDisplayName}\n                        onChange={(e) => setNewDisplayName(e.target.value)}\n                        placeholder=\"New display name\"\n                      />\n                      <button\n                        onClick={() => updateDisplayName(player.id, player.display_name)}\n                        disabled={loading}\n                        className=\"admin-button small\"\n                      >\n                        Save\n                      </button>\n                      <button\n                        onClick={() => {\n                          setEditingDisplayName(null);\n                          setNewDisplayName('');\n                        }}\n                        className=\"admin-button small secondary\"\n                      >\n                        Cancel\n                      </button>\n                    </div>\n                  ) : (\n                    <button\n                      onClick={() => {\n                        setEditingDisplayName(player.id);\n                        setNewDisplayName(player.display_name);\n                      }}\n                      disabled={loading}\n                      className=\"admin-button small\"\n                    >\n                      Edit Name\n                    </button>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Board Management */}\n        <div className=\"admin-section\">\n          <h2>Board Management</h2>\n          <div className=\"board-list\">\n            {boards.map(board => (\n              <div key={board.id} className=\"board-item\">\n                <div className=\"board-info\">\n                  <span className=\"board-player\">{board.display_name}</span>\n                  <span className=\"board-created\">Created: {new Date(board.created_at).toLocaleDateString()}</span>\n                </div>\n                <div className=\"board-actions\">\n                  <button\n                    onClick={() => deleteBoard(board.user_id, board.display_name)}\n                    disabled={loading}\n                    className=\"admin-button small danger\"\n                  >\n                    Delete Board\n                  </button>\n                  <button\n                    onClick={() => setEditingBoard(board)}\n                    disabled={loading}\n                    className=\"admin-button small\"\n                  >\n                    Edit Board\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Board Editor Modal */}\n      {editingBoard && (\n        <BoardEditor\n          board={editingBoard}\n          onClose={() => setEditingBoard(null)}\n          onUpdate={() => {\n            fetchBoards();\n            setEditingBoard(null);\n            showMessage('Board updated successfully');\n          }}\n        />\n      )}\n    </div>\n  );\n};\n\n// Board Editor Component\nconst BoardEditor = ({ board, onClose, onUpdate }) => {\n  const [boardProgress, setBoardProgress] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    fetchBoardProgress();\n  }, [board]);\n\n  const fetchBoardProgress = async () => {\n    try {\n      const response = await fetch(`http://localhost:5000/api/boards/${encodeURIComponent(board.display_name)}/progress`);\n      if (response.ok) {\n        const data = await response.json();\n        setBoardProgress(data);\n      }\n    } catch (error) {\n      console.error('Error fetching board progress:', error);\n    }\n  };\n\n  const toggleSquare = async (squareIndex) => {\n    if (!boardProgress) return;\n\n    setLoading(true);\n    try {\n      const markedCells = [...boardProgress.marked_cells];\n      const userImages = { ...boardProgress.user_images };\n\n      if (markedCells.includes(squareIndex)) {\n        // Unclaim the square\n        const index = markedCells.indexOf(squareIndex);\n        markedCells.splice(index, 1);\n        delete userImages[squareIndex];\n      } else {\n        // Claim the square\n        markedCells.push(squareIndex);\n      }\n\n      // Calculate new score\n      let newScore = 0;\n      markedCells.forEach(cellIndex => {\n        const character = board.board_data[cellIndex];\n        if (character) {\n          switch (character.rarity) {\n            case 'FREE': newScore += 1; break;\n            case 'R': newScore += 2; break;\n            case 'SR': newScore += 3; break;\n            case 'SSR': newScore += 4; break;\n            case 'UR+': newScore += 6; break;\n            default: newScore += 1; break;\n          }\n        }\n      });\n\n      // Check for completed rows/columns and add bonus points\n      const completedLines = checkCompletedLines(markedCells);\n      newScore += completedLines * 5;\n\n      const response = await fetch(`http://localhost:5000/api/admin/boards/${board.user_id}/progress`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          marked_cells: markedCells,\n          user_images: userImages,\n          score: newScore\n        }),\n      });\n\n      if (response.ok) {\n        const updatedProgress = await response.json();\n        setBoardProgress(updatedProgress);\n      }\n    } catch (error) {\n      console.error('Error updating board progress:', error);\n    }\n    setLoading(false);\n  };\n\n  const checkCompletedLines = (markedCells) => {\n    let completedLines = 0;\n\n    // Check rows\n    for (let row = 0; row < 5; row++) {\n      let rowComplete = true;\n      for (let col = 0; col < 5; col++) {\n        if (!markedCells.includes(row * 5 + col)) {\n          rowComplete = false;\n          break;\n        }\n      }\n      if (rowComplete) completedLines++;\n    }\n\n    // Check columns\n    for (let col = 0; col < 5; col++) {\n      let colComplete = true;\n      for (let row = 0; row < 5; row++) {\n        if (!markedCells.includes(row * 5 + col)) {\n          colComplete = false;\n          break;\n        }\n      }\n      if (colComplete) completedLines++;\n    }\n\n    return completedLines;\n  };\n\n  if (!boardProgress) {\n    return (\n      <div className=\"board-editor-overlay\">\n        <div className=\"board-editor\">\n          <div>Loading board...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"board-editor-overlay\">\n      <div className=\"board-editor\">\n        <div className=\"board-editor-header\">\n          <h3>Editing Board for {board.display_name}</h3>\n          <button onClick={onClose} className=\"admin-button secondary\">Close</button>\n        </div>\n\n        <div className=\"board-editor-content\">\n          <div className=\"board-grid\">\n            {board.board_data.map((character, index) => (\n              <div\n                key={index}\n                className={`board-square ${boardProgress.marked_cells.includes(index) ? 'claimed' : ''} ${character.rarity === 'FREE' ? 'free' : ''}`}\n                onClick={() => toggleSquare(index)}\n                style={{ cursor: loading ? 'not-allowed' : 'pointer' }}\n              >\n                {character.rarity === 'FREE' ? 'FREE' : character.name}\n              </div>\n            ))}\n          </div>\n\n          <div className=\"board-stats\">\n            <p>Score: {boardProgress.score}</p>\n            <p>Claimed Squares: {boardProgress.marked_cells.length}/25</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPanel;\n"], "mappings": "+IAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,OAAO,KAAQ,qBAAqB,CAC7C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAEC,IAAI,CAAEC,MAAO,CAAC,CAAGR,OAAO,CAAC,CAAC,CAClC,KAAM,CAAAS,QAAQ,CAAGR,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACS,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACc,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACgB,WAAW,CAAEC,cAAc,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAAE;AAEpD;AACA,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACoB,MAAM,CAAEC,SAAS,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACsB,cAAc,CAAEC,iBAAiB,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACwB,aAAa,CAAEC,gBAAgB,CAAC,CAAGzB,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAAC0B,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAClE,KAAM,CAAC4B,cAAc,CAAEC,iBAAiB,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC8B,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACgC,aAAa,CAAEC,gBAAgB,CAAC,CAAGjC,QAAQ,CAAC,IAAI,CAAC,CAExD;AACA,GAAI,CAACS,IAAI,EAAI,CAACA,IAAI,CAACyB,QAAQ,CAAE,CAC3B,mBAAO7B,IAAA,QAAK8B,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,2CAAyC,CAAK,CAAC,CACrF,CAEA;AACAnC,SAAS,CAAC,IAAM,CACdoC,YAAY,CAAC,CAAC,CACdC,WAAW,CAAC,CAAC,CACf,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,WAAW,CAAG,QAAAA,CAACC,GAAG,CAAuB,IAArB,CAAAC,IAAI,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,CACxC3B,UAAU,CAACyB,GAAG,CAAC,CACfvB,cAAc,CAACwB,IAAI,CAAC,CACpBI,UAAU,CAAC,IAAM,CACf9B,UAAU,CAAC,EAAE,CAAC,CACdE,cAAc,CAAC,EAAE,CAAC,CACpB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED,KAAM,CAAAoB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAS,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,iCAAiC,CAAC,CAC/D,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC/B,UAAU,CAAC8B,IAAI,CAAC,CAClB,CACF,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAb,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACF,KAAM,CAAAQ,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,wCAAwC,CAAC,CACtE,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC7B,SAAS,CAAC4B,IAAI,CAAC,CACjB,CACF,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAAE,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CAACC,MAAM,CAACC,OAAO,CAAC,8EAA8E,CAAC,CAAE,CACnG,OACF,CAEA1C,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAiC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,gDAAgD,CAAE,CAC7ES,MAAM,CAAE,MACV,CAAC,CAAC,CAEF,GAAIV,QAAQ,CAACE,EAAE,CAAE,CACfT,WAAW,CAAC,yDAAyD,CAAC,CACxE,CAAC,IAAM,CACLA,WAAW,CAAC,0BAA0B,CAAE,OAAO,CAAC,CAClD,CACF,CAAE,MAAOY,KAAK,CAAE,CACdZ,WAAW,CAAC,yBAAyB,CAAE,OAAO,CAAC,CACjD,CACA1B,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,KAAM,CAAA4C,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAACH,MAAM,CAACC,OAAO,CAAC,2EAA2E,CAAC,CAAE,CAChG,OACF,CAEA1C,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAiC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,mDAAmD,CAAE,CAChFS,MAAM,CAAE,QACV,CAAC,CAAC,CAEF,GAAIV,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCX,WAAW,CAACU,IAAI,CAACnC,OAAO,CAAC,CACzBwB,WAAW,CAAC,CAAC,CAAE;AACjB,CAAC,IAAM,CACLC,WAAW,CAAC,6BAA6B,CAAE,OAAO,CAAC,CACrD,CACF,CAAE,MAAOY,KAAK,CAAE,CACdZ,WAAW,CAAC,2BAA2B,CAAE,OAAO,CAAC,CACnD,CACA1B,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,KAAM,CAAA6C,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CAACJ,MAAM,CAACC,OAAO,CAAC,4EAA4E,CAAC,CAAE,CACjG,OACF,CAEA1C,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAiC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,oDAAoD,CAAE,CACjFS,MAAM,CAAE,QACV,CAAC,CAAC,CAEF,GAAIV,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCX,WAAW,CAACU,IAAI,CAACnC,OAAO,CAAC,CACzBuB,YAAY,CAAC,CAAC,CAAE;AAChBC,WAAW,CAAC,CAAC,CAAE;AACjB,CAAC,IAAM,CACLC,WAAW,CAAC,8BAA8B,CAAE,OAAO,CAAC,CACtD,CACF,CAAE,MAAOY,KAAK,CAAE,CACdZ,WAAW,CAAC,4BAA4B,CAAE,OAAO,CAAC,CACpD,CACA1B,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,KAAM,CAAA8C,YAAY,CAAG,KAAAA,CAAOC,QAAQ,CAAEC,UAAU,GAAK,CACnD,GAAI,CAACP,MAAM,CAACC,OAAO,6CAAAO,MAAA,CAA4CD,UAAU,qCAAkC,CAAC,CAAE,CAC5G,OACF,CAEAhD,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAiC,QAAQ,CAAG,KAAM,CAAAC,KAAK,kDAAAe,MAAA,CAAkDF,QAAQ,EAAI,CACxFJ,MAAM,CAAE,QACV,CAAC,CAAC,CAEF,GAAIV,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCX,WAAW,CAACU,IAAI,CAACnC,OAAO,CAAC,CACzBuB,YAAY,CAAC,CAAC,CAAE;AAChBC,WAAW,CAAC,CAAC,CAAE;AACjB,CAAC,IAAM,CACLC,WAAW,CAAC,yBAAyB,CAAE,OAAO,CAAC,CACjD,CACF,CAAE,MAAOY,KAAK,CAAE,CACdZ,WAAW,CAAC,uBAAuB,CAAE,OAAO,CAAC,CAC/C,CACA1B,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,KAAM,CAAAkD,WAAW,CAAG,KAAAA,CAAOC,MAAM,CAAEH,UAAU,GAAK,CAChD,GAAI,CAACP,MAAM,CAACC,OAAO,oDAAAO,MAAA,CAAmDD,UAAU,qCAAkC,CAAC,CAAE,CACnH,OACF,CAEAhD,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAiC,QAAQ,CAAG,KAAM,CAAAC,KAAK,iDAAAe,MAAA,CAAiDE,MAAM,EAAI,CACrFR,MAAM,CAAE,QACV,CAAC,CAAC,CAEF,GAAIV,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCX,WAAW,CAACU,IAAI,CAACnC,OAAO,CAAC,CACzBwB,WAAW,CAAC,CAAC,CAAE;AACjB,CAAC,IAAM,CACLC,WAAW,CAAC,wBAAwB,CAAE,OAAO,CAAC,CAChD,CACF,CAAE,MAAOY,KAAK,CAAE,CACdZ,WAAW,CAAC,sBAAsB,CAAE,OAAO,CAAC,CAC9C,CACA1B,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,KAAM,CAAAoD,iBAAiB,CAAG,KAAAA,CAAOD,MAAM,CAAEE,WAAW,GAAK,CACvD,GAAI,CAACtC,cAAc,CAACuC,IAAI,CAAC,CAAC,CAAE,CAC1B5B,WAAW,CAAC,iCAAiC,CAAE,OAAO,CAAC,CACvD,OACF,CAEA1B,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAiC,QAAQ,CAAG,KAAM,CAAAC,KAAK,wDAAAe,MAAA,CAAwDE,MAAM,EAAI,CAC5FR,MAAM,CAAE,KAAK,CACbY,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEC,YAAY,CAAE5C,cAAe,CAAC,CACvD,CAAC,CAAC,CAEF,GAAIkB,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCX,WAAW,CAACU,IAAI,CAACnC,OAAO,CAAC,CACzBuB,YAAY,CAAC,CAAC,CAAE;AAChBC,WAAW,CAAC,CAAC,CAAE;AACfX,qBAAqB,CAAC,IAAI,CAAC,CAC3BE,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAC,IAAM,CACL,KAAM,CAAAoB,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCX,WAAW,CAACU,IAAI,CAACE,KAAK,EAAI,+BAA+B,CAAE,OAAO,CAAC,CACrE,CACF,CAAE,MAAOA,KAAK,CAAE,CACdZ,WAAW,CAAC,6BAA6B,CAAE,OAAO,CAAC,CACrD,CACA1B,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,mBACEN,KAAA,QAAK4B,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7B,KAAA,QAAK4B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B/B,IAAA,OAAA+B,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB7B,KAAA,QAAK4B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7B,KAAA,MAAA6B,QAAA,EAAG,WAAS,CAAC3B,IAAI,CAAC+D,YAAY,EAAI,CAAC,cACnCnE,IAAA,WACEoE,OAAO,CAAEA,CAAA,GAAM,CACb/D,MAAM,CAAC,CAAC,CACRC,QAAQ,CAAC,GAAG,CAAC,CACf,CAAE,CACFwB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CAELtB,OAAO,eACNT,IAAA,QAAK8B,SAAS,kBAAA2B,MAAA,CAAmB9C,WAAW,CAAG,CAAAoB,QAAA,CAC5CtB,OAAO,CACL,CACN,cAEDP,KAAA,QAAK4B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAE7B7B,KAAA,QAAK4B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B/B,IAAA,OAAA+B,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1B/B,IAAA,WACEoE,OAAO,CAAEpB,aAAc,CACvBqB,QAAQ,CAAE9D,OAAQ,CAClBuB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAChC,gBAED,CAAQ,CAAC,EACN,CAAC,cAGN7B,KAAA,QAAK4B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B/B,IAAA,OAAA+B,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxB/B,IAAA,WACEoE,OAAO,CAAEhB,eAAgB,CACzBiB,QAAQ,CAAE9D,OAAQ,CAClBuB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAChC,mBAED,CAAQ,CAAC,cACT/B,IAAA,WACEoE,OAAO,CAAEf,gBAAiB,CAC1BgB,QAAQ,CAAE9D,OAAQ,CAClBuB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAChC,oBAED,CAAQ,CAAC,EACN,CAAC,cAGN7B,KAAA,QAAK4B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B/B,IAAA,OAAA+B,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1B/B,IAAA,QAAK8B,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBlB,OAAO,CAACyD,GAAG,CAACC,MAAM,eACjBrE,KAAA,QAAqB4B,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1C7B,KAAA,QAAK4B,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B/B,IAAA,SAAM8B,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEwC,MAAM,CAACJ,YAAY,CAAO,CAAC,cAC1DjE,KAAA,SAAM4B,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,OAAK,CAACwC,MAAM,CAACC,GAAG,EAAO,CAAC,EAClD,CAAC,cACNtE,KAAA,QAAK4B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/B,IAAA,WACEoE,OAAO,CAAEA,CAAA,GAAMd,YAAY,CAACiB,MAAM,CAACE,EAAE,CAAEF,MAAM,CAACJ,YAAY,CAAE,CAC5DE,QAAQ,CAAE9D,OAAQ,CAClBuB,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACtC,eAED,CAAQ,CAAC,CACRV,kBAAkB,GAAKkD,MAAM,CAACE,EAAE,cAC/BvE,KAAA,QAAK4B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/B,IAAA,UACEoC,IAAI,CAAC,MAAM,CACXsC,KAAK,CAAEnD,cAAe,CACtBoD,QAAQ,CAAGC,CAAC,EAAKpD,iBAAiB,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnDI,WAAW,CAAC,kBAAkB,CAC/B,CAAC,cACF9E,IAAA,WACEoE,OAAO,CAAEA,CAAA,GAAMR,iBAAiB,CAACW,MAAM,CAACE,EAAE,CAAEF,MAAM,CAACJ,YAAY,CAAE,CACjEE,QAAQ,CAAE9D,OAAQ,CAClBuB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAC/B,MAED,CAAQ,CAAC,cACT/B,IAAA,WACEoE,OAAO,CAAEA,CAAA,GAAM,CACb9C,qBAAqB,CAAC,IAAI,CAAC,CAC3BE,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAE,CACFM,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,QAED,CAAQ,CAAC,EACN,CAAC,cAEN/B,IAAA,WACEoE,OAAO,CAAEA,CAAA,GAAM,CACb9C,qBAAqB,CAACiD,MAAM,CAACE,EAAE,CAAC,CAChCjD,iBAAiB,CAAC+C,MAAM,CAACJ,YAAY,CAAC,CACxC,CAAE,CACFE,QAAQ,CAAE9D,OAAQ,CAClBuB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAC/B,WAED,CAAQ,CACT,EACE,CAAC,GAlDEwC,MAAM,CAACE,EAmDZ,CACN,CAAC,CACC,CAAC,EACH,CAAC,cAGNvE,KAAA,QAAK4B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B/B,IAAA,OAAA+B,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzB/B,IAAA,QAAK8B,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBhB,MAAM,CAACuD,GAAG,CAACS,KAAK,eACf7E,KAAA,QAAoB4B,SAAS,CAAC,YAAY,CAAAC,QAAA,eACxC7B,KAAA,QAAK4B,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB/B,IAAA,SAAM8B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEgD,KAAK,CAACZ,YAAY,CAAO,CAAC,cAC1DjE,KAAA,SAAM4B,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,WAAS,CAAC,GAAI,CAAAiD,IAAI,CAACD,KAAK,CAACE,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAO,CAAC,EAC9F,CAAC,cACNhF,KAAA,QAAK4B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B/B,IAAA,WACEoE,OAAO,CAAEA,CAAA,GAAMV,WAAW,CAACqB,KAAK,CAACI,OAAO,CAAEJ,KAAK,CAACZ,YAAY,CAAE,CAC9DE,QAAQ,CAAE9D,OAAQ,CAClBuB,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACtC,cAED,CAAQ,CAAC,cACT/B,IAAA,WACEoE,OAAO,CAAEA,CAAA,GAAM1C,eAAe,CAACqD,KAAK,CAAE,CACtCV,QAAQ,CAAE9D,OAAQ,CAClBuB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAC/B,YAED,CAAQ,CAAC,EACN,CAAC,GApBEgD,KAAK,CAACN,EAqBX,CACN,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CAGLhD,YAAY,eACXzB,IAAA,CAACoF,WAAW,EACVL,KAAK,CAAEtD,YAAa,CACpB4D,OAAO,CAAEA,CAAA,GAAM3D,eAAe,CAAC,IAAI,CAAE,CACrC4D,QAAQ,CAAEA,CAAA,GAAM,CACdrD,WAAW,CAAC,CAAC,CACbP,eAAe,CAAC,IAAI,CAAC,CACrBQ,WAAW,CAAC,4BAA4B,CAAC,CAC3C,CAAE,CACH,CACF,EACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAkD,WAAW,CAAGG,IAAA,EAAkC,IAAjC,CAAER,KAAK,CAAEM,OAAO,CAAEC,QAAS,CAAC,CAAAC,IAAA,CAC/C,KAAM,CAAC5D,aAAa,CAAEC,gBAAgB,CAAC,CAAGjC,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACY,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CAE7CC,SAAS,CAAC,IAAM,CACd4F,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAAE,CAACT,KAAK,CAAC,CAAC,CAEX,KAAM,CAAAS,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF,KAAM,CAAA/C,QAAQ,CAAG,KAAM,CAAAC,KAAK,qCAAAe,MAAA,CAAqCgC,kBAAkB,CAACV,KAAK,CAACZ,YAAY,CAAC,aAAW,CAAC,CACnH,GAAI1B,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCjB,gBAAgB,CAACgB,IAAI,CAAC,CACxB,CACF,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACxD,CACF,CAAC,CAED,KAAM,CAAA4C,YAAY,CAAG,KAAO,CAAAC,WAAW,EAAK,CAC1C,GAAI,CAAChE,aAAa,CAAE,OAEpBnB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAoF,WAAW,CAAG,CAAC,GAAGjE,aAAa,CAACkE,YAAY,CAAC,CACnD,KAAM,CAAAC,UAAU,CAAAC,aAAA,IAAQpE,aAAa,CAACqE,WAAW,CAAE,CAEnD,GAAIJ,WAAW,CAACK,QAAQ,CAACN,WAAW,CAAC,CAAE,CACrC;AACA,KAAM,CAAAO,KAAK,CAAGN,WAAW,CAACO,OAAO,CAACR,WAAW,CAAC,CAC9CC,WAAW,CAACQ,MAAM,CAACF,KAAK,CAAE,CAAC,CAAC,CAC5B,MAAO,CAAAJ,UAAU,CAACH,WAAW,CAAC,CAChC,CAAC,IAAM,CACL;AACAC,WAAW,CAACS,IAAI,CAACV,WAAW,CAAC,CAC/B,CAEA;AACA,GAAI,CAAAW,QAAQ,CAAG,CAAC,CAChBV,WAAW,CAACW,OAAO,CAACC,SAAS,EAAI,CAC/B,KAAM,CAAAC,SAAS,CAAG1B,KAAK,CAAC2B,UAAU,CAACF,SAAS,CAAC,CAC7C,GAAIC,SAAS,CAAE,CACb,OAAQA,SAAS,CAACE,MAAM,EACtB,IAAK,MAAM,CAAEL,QAAQ,EAAI,CAAC,CAAE,MAC5B,IAAK,GAAG,CAAEA,QAAQ,EAAI,CAAC,CAAE,MACzB,IAAK,IAAI,CAAEA,QAAQ,EAAI,CAAC,CAAE,MAC1B,IAAK,KAAK,CAAEA,QAAQ,EAAI,CAAC,CAAE,MAC3B,IAAK,KAAK,CAAEA,QAAQ,EAAI,CAAC,CAAE,MAC3B,QAASA,QAAQ,EAAI,CAAC,CAAE,MAC1B,CACF,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAM,cAAc,CAAGC,mBAAmB,CAACjB,WAAW,CAAC,CACvDU,QAAQ,EAAIM,cAAc,CAAG,CAAC,CAE9B,KAAM,CAAAnE,QAAQ,CAAG,KAAM,CAAAC,KAAK,2CAAAe,MAAA,CAA2CsB,KAAK,CAACI,OAAO,cAAa,CAC/FhC,MAAM,CAAE,KAAK,CACbY,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnB2B,YAAY,CAAED,WAAW,CACzBI,WAAW,CAAEF,UAAU,CACvBgB,KAAK,CAAER,QACT,CAAC,CACH,CAAC,CAAC,CAEF,GAAI7D,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAoE,eAAe,CAAG,KAAM,CAAAtE,QAAQ,CAACI,IAAI,CAAC,CAAC,CAC7CjB,gBAAgB,CAACmF,eAAe,CAAC,CACnC,CACF,CAAE,MAAOjE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACxD,CACAtC,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,KAAM,CAAAqG,mBAAmB,CAAIjB,WAAW,EAAK,CAC3C,GAAI,CAAAgB,cAAc,CAAG,CAAC,CAEtB;AACA,IAAK,GAAI,CAAAI,GAAG,CAAG,CAAC,CAAEA,GAAG,CAAG,CAAC,CAAEA,GAAG,EAAE,CAAE,CAChC,GAAI,CAAAC,WAAW,CAAG,IAAI,CACtB,IAAK,GAAI,CAAAC,GAAG,CAAG,CAAC,CAAEA,GAAG,CAAG,CAAC,CAAEA,GAAG,EAAE,CAAE,CAChC,GAAI,CAACtB,WAAW,CAACK,QAAQ,CAACe,GAAG,CAAG,CAAC,CAAGE,GAAG,CAAC,CAAE,CACxCD,WAAW,CAAG,KAAK,CACnB,MACF,CACF,CACA,GAAIA,WAAW,CAAEL,cAAc,EAAE,CACnC,CAEA;AACA,IAAK,GAAI,CAAAM,GAAG,CAAG,CAAC,CAAEA,GAAG,CAAG,CAAC,CAAEA,GAAG,EAAE,CAAE,CAChC,GAAI,CAAAC,WAAW,CAAG,IAAI,CACtB,IAAK,GAAI,CAAAH,GAAG,CAAG,CAAC,CAAEA,GAAG,CAAG,CAAC,CAAEA,GAAG,EAAE,CAAE,CAChC,GAAI,CAACpB,WAAW,CAACK,QAAQ,CAACe,GAAG,CAAG,CAAC,CAAGE,GAAG,CAAC,CAAE,CACxCC,WAAW,CAAG,KAAK,CACnB,MACF,CACF,CACA,GAAIA,WAAW,CAAEP,cAAc,EAAE,CACnC,CAEA,MAAO,CAAAA,cAAc,CACvB,CAAC,CAED,GAAI,CAACjF,aAAa,CAAE,CAClB,mBACE3B,IAAA,QAAK8B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnC/B,IAAA,QAAK8B,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B/B,IAAA,QAAA+B,QAAA,CAAK,kBAAgB,CAAK,CAAC,CACxB,CAAC,CACH,CAAC,CAEV,CAEA,mBACE/B,IAAA,QAAK8B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnC7B,KAAA,QAAK4B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7B,KAAA,QAAK4B,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC7B,KAAA,OAAA6B,QAAA,EAAI,oBAAkB,CAACgD,KAAK,CAACZ,YAAY,EAAK,CAAC,cAC/CnE,IAAA,WAAQoE,OAAO,CAAEiB,OAAQ,CAACvD,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,OAAK,CAAQ,CAAC,EACxE,CAAC,cAEN7B,KAAA,QAAK4B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC/B,IAAA,QAAK8B,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBgD,KAAK,CAAC2B,UAAU,CAACpC,GAAG,CAAC,CAACmC,SAAS,CAAEP,KAAK,gBACrClG,IAAA,QAEE8B,SAAS,iBAAA2B,MAAA,CAAkB9B,aAAa,CAACkE,YAAY,CAACI,QAAQ,CAACC,KAAK,CAAC,CAAG,SAAS,CAAG,EAAE,MAAAzC,MAAA,CAAIgD,SAAS,CAACE,MAAM,GAAK,MAAM,CAAG,MAAM,CAAG,EAAE,CAAG,CACtIvC,OAAO,CAAEA,CAAA,GAAMsB,YAAY,CAACQ,KAAK,CAAE,CACnCkB,KAAK,CAAE,CAAEC,MAAM,CAAE9G,OAAO,CAAG,aAAa,CAAG,SAAU,CAAE,CAAAwB,QAAA,CAEtD0E,SAAS,CAACE,MAAM,GAAK,MAAM,CAAG,MAAM,CAAGF,SAAS,CAACa,IAAI,EALjDpB,KAMF,CACN,CAAC,CACC,CAAC,cAENhG,KAAA,QAAK4B,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7B,KAAA,MAAA6B,QAAA,EAAG,SAAO,CAACJ,aAAa,CAACmF,KAAK,EAAI,CAAC,cACnC5G,KAAA,MAAA6B,QAAA,EAAG,mBAAiB,CAACJ,aAAa,CAACkE,YAAY,CAACvD,MAAM,CAAC,KAAG,EAAG,CAAC,EAC3D,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}