{"ast": null, "code": "import React,{createContext,useState,useEffect,useContext}from'react';// Create the user context\nimport{jsx as _jsx}from\"react/jsx-runtime\";const UserContext=/*#__PURE__*/createContext();// Custom hook to use the user context\nexport const useUser=()=>useContext(UserContext);// User provider component\nexport const UserProvider=_ref=>{let{children}=_ref;// State for user data\nconst[user,setUser]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);// Check for existing user session on mount\nuseEffect(()=>{const checkUserSession=async()=>{try{// Check if user data exists in local storage\nconst storedUser=localStorage.getItem('user');if(storedUser){const userData=JSON.parse(storedUser);// Verify the user still exists in the database\nconst response=await fetch(\"http://localhost:5000/api/auth/login\",{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({pin:userData.pin})});if(response.ok){const data=await response.json();if(data.success){setUser(data.user);}else{// Clear invalid session\nlocalStorage.removeItem('user');setUser(null);}}else{// Clear invalid session\nlocalStorage.removeItem('user');setUser(null);}}}catch(err){console.error('Error checking user session:',err);setError('Failed to restore user session');}finally{setLoading(false);}};checkUserSession();},[]);// Login function\nconst login=async pin=>{try{setLoading(true);setError(null);const response=await fetch('http://localhost:5000/api/auth/login',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({pin})});const data=await response.json();if(response.ok&&data.success){// Save user data to state and local storage\nsetUser(data.user);localStorage.setItem('user',JSON.stringify(data.user));return{success:true,user:data.user};}else{setError(data.error||'Invalid PIN');return{success:false,error:data.error||'Invalid PIN'};}}catch(err){console.error('Login error:',err);setError('Failed to login. Please try again.');return{success:false,error:'Failed to login. Please try again.'};}finally{setLoading(false);}};// Register function\nconst register=async(pin,displayName)=>{try{setLoading(true);setError(null);const response=await fetch('http://localhost:5000/api/auth/register',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({pin,display_name:displayName})});const data=await response.json();if(response.ok&&data.success){// Save user data to state and local storage\nsetUser(data.user);localStorage.setItem('user',JSON.stringify(data.user));return{success:true};}else{setError(data.error||'Failed to register');return{success:false,error:data.error||'Failed to register'};}}catch(err){console.error('Registration error:',err);setError('Failed to register. Please try again.');return{success:false,error:'Failed to register. Please try again.'};}finally{setLoading(false);}};// Logout function\nconst logout=()=>{localStorage.removeItem('user');setUser(null);};// Context value\nconst value={user,loading,error,login,register,logout};return/*#__PURE__*/_jsx(UserContext.Provider,{value:value,children:children});};export default UserContext;", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "useContext", "jsx", "_jsx", "UserContext", "useUser", "UserProvider", "_ref", "children", "user", "setUser", "loading", "setLoading", "error", "setError", "checkUserSession", "storedUser", "localStorage", "getItem", "userData", "JSON", "parse", "response", "fetch", "method", "headers", "body", "stringify", "pin", "ok", "data", "json", "success", "removeItem", "err", "console", "login", "setItem", "register", "displayName", "display_name", "logout", "value", "Provider"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Auth/UserContext.js"], "sourcesContent": ["import React, { createContext, useState, useEffect, useContext } from 'react';\r\n\r\n// Create the user context\r\nconst UserContext = createContext();\r\n\r\n// Custom hook to use the user context\r\nexport const useUser = () => useContext(UserContext);\r\n\r\n// User provider component\r\nexport const UserProvider = ({ children }) => {\r\n  // State for user data\r\n  const [user, setUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  // Check for existing user session on mount\r\n  useEffect(() => {\r\n    const checkUserSession = async () => {\r\n      try {\r\n        // Check if user data exists in local storage\r\n        const storedUser = localStorage.getItem('user');\r\n        \r\n        if (storedUser) {\r\n          const userData = JSON.parse(storedUser);\r\n          \r\n          // Verify the user still exists in the database\r\n          const response = await fetch(`http://localhost:5000/api/auth/login`, {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify({ pin: userData.pin }),\r\n          });\r\n          \r\n          if (response.ok) {\r\n            const data = await response.json();\r\n            if (data.success) {\r\n              setUser(data.user);\r\n            } else {\r\n              // Clear invalid session\r\n              localStorage.removeItem('user');\r\n              setUser(null);\r\n            }\r\n          } else {\r\n            // Clear invalid session\r\n            localStorage.removeItem('user');\r\n            setUser(null);\r\n          }\r\n        }\r\n      } catch (err) {\r\n        console.error('Error checking user session:', err);\r\n        setError('Failed to restore user session');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    checkUserSession();\r\n  }, []);\r\n\r\n  // Login function\r\n  const login = async (pin) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      const response = await fetch('http://localhost:5000/api/auth/login', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ pin }),\r\n      });\r\n      \r\n      const data = await response.json();\r\n      \r\n      if (response.ok && data.success) {\r\n        // Save user data to state and local storage\r\n        setUser(data.user);\r\n        localStorage.setItem('user', JSON.stringify(data.user));\r\n        return { success: true, user: data.user };\r\n      } else {\r\n        setError(data.error || 'Invalid PIN');\r\n        return { success: false, error: data.error || 'Invalid PIN' };\r\n      }\r\n    } catch (err) {\r\n      console.error('Login error:', err);\r\n      setError('Failed to login. Please try again.');\r\n      return { success: false, error: 'Failed to login. Please try again.' };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Register function\r\n  const register = async (pin, displayName) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      const response = await fetch('http://localhost:5000/api/auth/register', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ pin, display_name: displayName }),\r\n      });\r\n      \r\n      const data = await response.json();\r\n      \r\n      if (response.ok && data.success) {\r\n        // Save user data to state and local storage\r\n        setUser(data.user);\r\n        localStorage.setItem('user', JSON.stringify(data.user));\r\n        return { success: true };\r\n      } else {\r\n        setError(data.error || 'Failed to register');\r\n        return { success: false, error: data.error || 'Failed to register' };\r\n      }\r\n    } catch (err) {\r\n      console.error('Registration error:', err);\r\n      setError('Failed to register. Please try again.');\r\n      return { success: false, error: 'Failed to register. Please try again.' };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Logout function\r\n  const logout = () => {\r\n    localStorage.removeItem('user');\r\n    setUser(null);\r\n  };\r\n\r\n  // Context value\r\n  const value = {\r\n    user,\r\n    loading,\r\n    error,\r\n    login,\r\n    register,\r\n    logout,\r\n  };\r\n\r\n  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;\r\n};\r\n\r\nexport default UserContext;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,QAAQ,CAAEC,SAAS,CAAEC,UAAU,KAAQ,OAAO,CAE7E;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBACA,KAAM,CAAAC,WAAW,cAAGN,aAAa,CAAC,CAAC,CAEnC;AACA,MAAO,MAAM,CAAAO,OAAO,CAAGA,CAAA,GAAMJ,UAAU,CAACG,WAAW,CAAC,CAEpD;AACA,MAAO,MAAM,CAAAE,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC;AACA,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGX,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACY,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACc,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAExC;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAe,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAE/C,GAAIF,UAAU,CAAE,CACd,KAAM,CAAAG,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,CAEvC;AACA,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAC,KAAK,wCAAyC,CACnEC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEN,IAAI,CAACO,SAAS,CAAC,CAAEC,GAAG,CAAET,QAAQ,CAACS,GAAI,CAAC,CAC5C,CAAC,CAAC,CAEF,GAAIN,QAAQ,CAACO,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAR,QAAQ,CAACS,IAAI,CAAC,CAAC,CAClC,GAAID,IAAI,CAACE,OAAO,CAAE,CAChBtB,OAAO,CAACoB,IAAI,CAACrB,IAAI,CAAC,CACpB,CAAC,IAAM,CACL;AACAQ,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC,CAC/BvB,OAAO,CAAC,IAAI,CAAC,CACf,CACF,CAAC,IAAM,CACL;AACAO,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC,CAC/BvB,OAAO,CAAC,IAAI,CAAC,CACf,CACF,CACF,CAAE,MAAOwB,GAAG,CAAE,CACZC,OAAO,CAACtB,KAAK,CAAC,8BAA8B,CAAEqB,GAAG,CAAC,CAClDpB,QAAQ,CAAC,gCAAgC,CAAC,CAC5C,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDG,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAqB,KAAK,CAAG,KAAO,CAAAR,GAAG,EAAK,CAC3B,GAAI,CACFhB,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAQ,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,sCAAsC,CAAE,CACnEC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEN,IAAI,CAACO,SAAS,CAAC,CAAEC,GAAI,CAAC,CAC9B,CAAC,CAAC,CAEF,KAAM,CAAAE,IAAI,CAAG,KAAM,CAAAR,QAAQ,CAACS,IAAI,CAAC,CAAC,CAElC,GAAIT,QAAQ,CAACO,EAAE,EAAIC,IAAI,CAACE,OAAO,CAAE,CAC/B;AACAtB,OAAO,CAACoB,IAAI,CAACrB,IAAI,CAAC,CAClBQ,YAAY,CAACoB,OAAO,CAAC,MAAM,CAAEjB,IAAI,CAACO,SAAS,CAACG,IAAI,CAACrB,IAAI,CAAC,CAAC,CACvD,MAAO,CAAEuB,OAAO,CAAE,IAAI,CAAEvB,IAAI,CAAEqB,IAAI,CAACrB,IAAK,CAAC,CAC3C,CAAC,IAAM,CACLK,QAAQ,CAACgB,IAAI,CAACjB,KAAK,EAAI,aAAa,CAAC,CACrC,MAAO,CAAEmB,OAAO,CAAE,KAAK,CAAEnB,KAAK,CAAEiB,IAAI,CAACjB,KAAK,EAAI,aAAc,CAAC,CAC/D,CACF,CAAE,MAAOqB,GAAG,CAAE,CACZC,OAAO,CAACtB,KAAK,CAAC,cAAc,CAAEqB,GAAG,CAAC,CAClCpB,QAAQ,CAAC,oCAAoC,CAAC,CAC9C,MAAO,CAAEkB,OAAO,CAAE,KAAK,CAAEnB,KAAK,CAAE,oCAAqC,CAAC,CACxE,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA0B,QAAQ,CAAG,KAAAA,CAAOV,GAAG,CAAEW,WAAW,GAAK,CAC3C,GAAI,CACF3B,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAQ,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,yCAAyC,CAAE,CACtEC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEN,IAAI,CAACO,SAAS,CAAC,CAAEC,GAAG,CAAEY,YAAY,CAAED,WAAY,CAAC,CACzD,CAAC,CAAC,CAEF,KAAM,CAAAT,IAAI,CAAG,KAAM,CAAAR,QAAQ,CAACS,IAAI,CAAC,CAAC,CAElC,GAAIT,QAAQ,CAACO,EAAE,EAAIC,IAAI,CAACE,OAAO,CAAE,CAC/B;AACAtB,OAAO,CAACoB,IAAI,CAACrB,IAAI,CAAC,CAClBQ,YAAY,CAACoB,OAAO,CAAC,MAAM,CAAEjB,IAAI,CAACO,SAAS,CAACG,IAAI,CAACrB,IAAI,CAAC,CAAC,CACvD,MAAO,CAAEuB,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAC,IAAM,CACLlB,QAAQ,CAACgB,IAAI,CAACjB,KAAK,EAAI,oBAAoB,CAAC,CAC5C,MAAO,CAAEmB,OAAO,CAAE,KAAK,CAAEnB,KAAK,CAAEiB,IAAI,CAACjB,KAAK,EAAI,oBAAqB,CAAC,CACtE,CACF,CAAE,MAAOqB,GAAG,CAAE,CACZC,OAAO,CAACtB,KAAK,CAAC,qBAAqB,CAAEqB,GAAG,CAAC,CACzCpB,QAAQ,CAAC,uCAAuC,CAAC,CACjD,MAAO,CAAEkB,OAAO,CAAE,KAAK,CAAEnB,KAAK,CAAE,uCAAwC,CAAC,CAC3E,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA6B,MAAM,CAAGA,CAAA,GAAM,CACnBxB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC,CAC/BvB,OAAO,CAAC,IAAI,CAAC,CACf,CAAC,CAED;AACA,KAAM,CAAAgC,KAAK,CAAG,CACZjC,IAAI,CACJE,OAAO,CACPE,KAAK,CACLuB,KAAK,CACLE,QAAQ,CACRG,MACF,CAAC,CAED,mBAAOtC,IAAA,CAACC,WAAW,CAACuC,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAlC,QAAA,CAAEA,QAAQ,CAAuB,CAAC,CAC9E,CAAC,CAED,cAAe,CAAAJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}