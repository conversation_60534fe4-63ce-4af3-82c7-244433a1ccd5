{"ast": null, "code": "import React,{useState}from'react';import{useUser}from'./UserContext';import{useNavigate}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Login=()=>{// State for form inputs\nconst[pin,setPin]=useState('');const[displayName,setDisplayName]=useState('');const[isRegistering,setIsRegistering]=useState(false);const[formError,setFormError]=useState('');// Get user context and navigation\nconst{login,register,loading}=useUser();const navigate=useNavigate();// Handle form submission\nconst handleSubmit=async e=>{e.preventDefault();setFormError('');if(isRegistering){// Validate registration inputs\nif(!pin||!displayName){setFormError('Please enter both PIN and display name');return;}// Register new user\nconst result=await register(pin,displayName);if(result.success){// Redirect to dashboard on success (new users won't have boards yet)\nnavigate('/dashboard');}else{setFormError(result.error);}}else{// Validate login input\nif(!pin){setFormError('Please enter your PIN');return;}// Login existing user\nconst result=await login(pin);if(result.success){// Check if user is admin\nif(result.user&&result.user.is_admin){navigate('/admin');return;}// Check if user has a board and redirect accordingly\ntry{var _result$user;const boardResponse=await fetch(\"http://localhost:5000/api/users/\".concat(((_result$user=result.user)===null||_result$user===void 0?void 0:_result$user.id)||JSON.parse(localStorage.getItem('user')).id,\"/board\"),{method:'GET',headers:{'Content-Type':'application/json'}});if(boardResponse.ok){// User has a board, redirect to their board\nconst userData=JSON.parse(localStorage.getItem('user'));navigate(\"/boards/\".concat(encodeURIComponent(userData.display_name)));}else{// User doesn't have a board, redirect to dashboard\nnavigate('/dashboard');}}catch(err){console.error('Error checking user board:',err);// Fallback to dashboard\nnavigate('/dashboard');}}else{setFormError(result.error);}}};// Toggle between login and registration forms\nconst toggleForm=()=>{setIsRegistering(!isRegistering);setFormError('');};return/*#__PURE__*/_jsx(\"div\",{className:\"auth-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"auth-form-container\",children:[/*#__PURE__*/_jsx(\"h2\",{children:isRegistering?'Create Account':'Login'}),formError&&/*#__PURE__*/_jsx(\"div\",{className:\"auth-error\",children:formError}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"auth-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"pin\",children:\"PIN\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"pin\",value:pin,onChange:e=>setPin(e.target.value),placeholder:\"Enter your PIN\",disabled:loading})]}),isRegistering&&/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"displayName\",children:\"Display Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"displayName\",value:displayName,onChange:e=>setDisplayName(e.target.value),placeholder:\"Enter your display name\",disabled:loading})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"auth-button\",disabled:loading,children:loading?'Processing...':isRegistering?'Register':'Login'})]}),/*#__PURE__*/_jsx(\"div\",{className:\"auth-toggle\",children:/*#__PURE__*/_jsx(\"button\",{onClick:toggleForm,className:\"toggle-button\",disabled:loading,children:isRegistering?'Already have an account? Login':'New user? Create account'})})]})});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "useUser", "useNavigate", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "pin", "setPin", "displayName", "setDisplayName", "isRegistering", "setIsRegistering", "formError", "setFormError", "login", "register", "loading", "navigate", "handleSubmit", "e", "preventDefault", "result", "success", "error", "user", "is_admin", "_result$user", "boardResponse", "fetch", "concat", "id", "JSON", "parse", "localStorage", "getItem", "method", "headers", "ok", "userData", "encodeURIComponent", "display_name", "err", "console", "toggleForm", "className", "children", "onSubmit", "htmlFor", "type", "value", "onChange", "target", "placeholder", "disabled", "onClick"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { useUser } from './UserContext';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst Login = () => {\r\n  // State for form inputs\r\n  const [pin, setPin] = useState('');\r\n  const [displayName, setDisplayName] = useState('');\r\n  const [isRegistering, setIsRegistering] = useState(false);\r\n  const [formError, setFormError] = useState('');\r\n\r\n  // Get user context and navigation\r\n  const { login, register, loading } = useUser();\r\n  const navigate = useNavigate();\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setFormError('');\r\n\r\n    if (isRegistering) {\r\n      // Validate registration inputs\r\n      if (!pin || !displayName) {\r\n        setFormError('Please enter both PIN and display name');\r\n        return;\r\n      }\r\n\r\n      // Register new user\r\n      const result = await register(pin, displayName);\r\n\r\n      if (result.success) {\r\n        // Redirect to dashboard on success (new users won't have boards yet)\r\n        navigate('/dashboard');\r\n      } else {\r\n        setFormError(result.error);\r\n      }\r\n    } else {\r\n      // Validate login input\r\n      if (!pin) {\r\n        setFormError('Please enter your PIN');\r\n        return;\r\n      }\r\n\r\n      // Login existing user\r\n      const result = await login(pin);\r\n\r\n      if (result.success) {\r\n        // Check if user is admin\r\n        if (result.user && result.user.is_admin) {\r\n          navigate('/admin');\r\n          return;\r\n        }\r\n\r\n        // Check if user has a board and redirect accordingly\r\n        try {\r\n          const boardResponse = await fetch(`http://localhost:5000/api/users/${result.user?.id || JSON.parse(localStorage.getItem('user')).id}/board`, {\r\n            method: 'GET',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n          });\r\n\r\n          if (boardResponse.ok) {\r\n            // User has a board, redirect to their board\r\n            const userData = JSON.parse(localStorage.getItem('user'));\r\n            navigate(`/boards/${encodeURIComponent(userData.display_name)}`);\r\n          } else {\r\n            // User doesn't have a board, redirect to dashboard\r\n            navigate('/dashboard');\r\n          }\r\n        } catch (err) {\r\n          console.error('Error checking user board:', err);\r\n          // Fallback to dashboard\r\n          navigate('/dashboard');\r\n        }\r\n      } else {\r\n        setFormError(result.error);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Toggle between login and registration forms\r\n  const toggleForm = () => {\r\n    setIsRegistering(!isRegistering);\r\n    setFormError('');\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-form-container\">\r\n        <h2>{isRegistering ? 'Create Account' : 'Login'}</h2>\r\n\r\n        {formError && <div className=\"auth-error\">{formError}</div>}\r\n\r\n        <form onSubmit={handleSubmit} className=\"auth-form\">\r\n          <div className=\"form-group\">\r\n            <label htmlFor=\"pin\">PIN</label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"pin\"\r\n              value={pin}\r\n              onChange={(e) => setPin(e.target.value)}\r\n              placeholder=\"Enter your PIN\"\r\n              disabled={loading}\r\n            />\r\n          </div>\r\n\r\n          {isRegistering && (\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"displayName\">Display Name</label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"displayName\"\r\n                value={displayName}\r\n                onChange={(e) => setDisplayName(e.target.value)}\r\n                placeholder=\"Enter your display name\"\r\n                disabled={loading}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <button type=\"submit\" className=\"auth-button\" disabled={loading}>\r\n            {loading ? 'Processing...' : isRegistering ? 'Register' : 'Login'}\r\n          </button>\r\n        </form>\r\n\r\n        <div className=\"auth-toggle\">\r\n          <button onClick={toggleForm} className=\"toggle-button\" disabled={loading}>\r\n            {isRegistering ? 'Already have an account? Login' : 'New user? Create account'}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,OAAO,KAAQ,eAAe,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,KAAK,CAAGA,CAAA,GAAM,CAClB;AACA,KAAM,CAACC,GAAG,CAAEC,MAAM,CAAC,CAAGT,QAAQ,CAAC,EAAE,CAAC,CAClC,KAAM,CAACU,WAAW,CAAEC,cAAc,CAAC,CAAGX,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACY,aAAa,CAAEC,gBAAgB,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACc,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAE9C;AACA,KAAM,CAAEgB,KAAK,CAAEC,QAAQ,CAAEC,OAAQ,CAAC,CAAGjB,OAAO,CAAC,CAAC,CAC9C,KAAM,CAAAkB,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAkB,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBP,YAAY,CAAC,EAAE,CAAC,CAEhB,GAAIH,aAAa,CAAE,CACjB;AACA,GAAI,CAACJ,GAAG,EAAI,CAACE,WAAW,CAAE,CACxBK,YAAY,CAAC,wCAAwC,CAAC,CACtD,OACF,CAEA;AACA,KAAM,CAAAQ,MAAM,CAAG,KAAM,CAAAN,QAAQ,CAACT,GAAG,CAAEE,WAAW,CAAC,CAE/C,GAAIa,MAAM,CAACC,OAAO,CAAE,CAClB;AACAL,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAC,IAAM,CACLJ,YAAY,CAACQ,MAAM,CAACE,KAAK,CAAC,CAC5B,CACF,CAAC,IAAM,CACL;AACA,GAAI,CAACjB,GAAG,CAAE,CACRO,YAAY,CAAC,uBAAuB,CAAC,CACrC,OACF,CAEA;AACA,KAAM,CAAAQ,MAAM,CAAG,KAAM,CAAAP,KAAK,CAACR,GAAG,CAAC,CAE/B,GAAIe,MAAM,CAACC,OAAO,CAAE,CAClB;AACA,GAAID,MAAM,CAACG,IAAI,EAAIH,MAAM,CAACG,IAAI,CAACC,QAAQ,CAAE,CACvCR,QAAQ,CAAC,QAAQ,CAAC,CAClB,OACF,CAEA;AACA,GAAI,KAAAS,YAAA,CACF,KAAM,CAAAC,aAAa,CAAG,KAAM,CAAAC,KAAK,oCAAAC,MAAA,CAAoC,EAAAH,YAAA,CAAAL,MAAM,CAACG,IAAI,UAAAE,YAAA,iBAAXA,YAAA,CAAaI,EAAE,GAAIC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACJ,EAAE,WAAU,CAC3IK,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,GAAIT,aAAa,CAACU,EAAE,CAAE,CACpB;AACA,KAAM,CAAAC,QAAQ,CAAGP,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CACzDjB,QAAQ,YAAAY,MAAA,CAAYU,kBAAkB,CAACD,QAAQ,CAACE,YAAY,CAAC,CAAE,CAAC,CAClE,CAAC,IAAM,CACL;AACAvB,QAAQ,CAAC,YAAY,CAAC,CACxB,CACF,CAAE,MAAOwB,GAAG,CAAE,CACZC,OAAO,CAACnB,KAAK,CAAC,4BAA4B,CAAEkB,GAAG,CAAC,CAChD;AACAxB,QAAQ,CAAC,YAAY,CAAC,CACxB,CACF,CAAC,IAAM,CACLJ,YAAY,CAACQ,MAAM,CAACE,KAAK,CAAC,CAC5B,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAoB,UAAU,CAAGA,CAAA,GAAM,CACvBhC,gBAAgB,CAAC,CAACD,aAAa,CAAC,CAChCG,YAAY,CAAC,EAAE,CAAC,CAClB,CAAC,CAED,mBACEX,IAAA,QAAK0C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BzC,KAAA,QAAKwC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC3C,IAAA,OAAA2C,QAAA,CAAKnC,aAAa,CAAG,gBAAgB,CAAG,OAAO,CAAK,CAAC,CAEpDE,SAAS,eAAIV,IAAA,QAAK0C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEjC,SAAS,CAAM,CAAC,cAE3DR,KAAA,SAAM0C,QAAQ,CAAE5B,YAAa,CAAC0B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACjDzC,KAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3C,IAAA,UAAO6C,OAAO,CAAC,KAAK,CAAAF,QAAA,CAAC,KAAG,CAAO,CAAC,cAChC3C,IAAA,UACE8C,IAAI,CAAC,MAAM,CACXlB,EAAE,CAAC,KAAK,CACRmB,KAAK,CAAE3C,GAAI,CACX4C,QAAQ,CAAG/B,CAAC,EAAKZ,MAAM,CAACY,CAAC,CAACgC,MAAM,CAACF,KAAK,CAAE,CACxCG,WAAW,CAAC,gBAAgB,CAC5BC,QAAQ,CAAErC,OAAQ,CACnB,CAAC,EACC,CAAC,CAELN,aAAa,eACZN,KAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3C,IAAA,UAAO6C,OAAO,CAAC,aAAa,CAAAF,QAAA,CAAC,cAAY,CAAO,CAAC,cACjD3C,IAAA,UACE8C,IAAI,CAAC,MAAM,CACXlB,EAAE,CAAC,aAAa,CAChBmB,KAAK,CAAEzC,WAAY,CACnB0C,QAAQ,CAAG/B,CAAC,EAAKV,cAAc,CAACU,CAAC,CAACgC,MAAM,CAACF,KAAK,CAAE,CAChDG,WAAW,CAAC,yBAAyB,CACrCC,QAAQ,CAAErC,OAAQ,CACnB,CAAC,EACC,CACN,cAEDd,IAAA,WAAQ8C,IAAI,CAAC,QAAQ,CAACJ,SAAS,CAAC,aAAa,CAACS,QAAQ,CAAErC,OAAQ,CAAA6B,QAAA,CAC7D7B,OAAO,CAAG,eAAe,CAAGN,aAAa,CAAG,UAAU,CAAG,OAAO,CAC3D,CAAC,EACL,CAAC,cAEPR,IAAA,QAAK0C,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B3C,IAAA,WAAQoD,OAAO,CAAEX,UAAW,CAACC,SAAS,CAAC,eAAe,CAACS,QAAQ,CAAErC,OAAQ,CAAA6B,QAAA,CACtEnC,aAAa,CAAG,gCAAgC,CAAG,0BAA0B,CACxE,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}