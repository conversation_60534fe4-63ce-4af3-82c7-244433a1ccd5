{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Leaderboard=()=>{const navigate=useNavigate();// State for leaderboard data\nconst[leaderboardData,setLeaderboardData]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);// Fetch leaderboard data\nuseEffect(()=>{const fetchLeaderboard=async()=>{try{setLoading(true);const response=await fetch('http://localhost:5000/api/leaderboard');if(!response.ok){throw new Error(\"HTTP error! Status: \".concat(response.status));}const data=await response.json();setLeaderboardData(data);}catch(err){console.error('Error fetching leaderboard:',err);setError('Failed to load leaderboard data');}finally{setLoading(false);}};fetchLeaderboard();// Set up polling to refresh leaderboard data\nconst intervalId=setInterval(fetchLeaderboard,30000);// Refresh every 30 seconds\nreturn()=>clearInterval(intervalId);// Clean up on unmount\n},[]);// Sort data by score in descending order\nconst sortedData=[...leaderboardData].sort((a,b)=>b.score-a.score);// Find the maximum score for scaling\nconst maxScore=Math.max(...sortedData.map(user=>user.score||0),1);// Ensure we don't divide by zero\n// Truncate display name to max 16 characters\nconst truncateDisplayName=displayName=>{if(displayName.length<=16){return displayName;}return displayName.substring(0,16)+'...';};// Handle clicking on a leaderboard entry to view their board\nconst handleViewUserBoard=displayName=>{navigate(\"/boards/\".concat(encodeURIComponent(displayName)));};if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"leaderboard-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Leaderboard\"}),/*#__PURE__*/_jsx(\"div\",{className:\"loading-message\",children:\"Loading leaderboard...\"})]});}if(error){return/*#__PURE__*/_jsxs(\"div\",{className:\"leaderboard-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Leaderboard\"}),/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:error})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"leaderboard-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Leaderboard\"}),/*#__PURE__*/_jsx(\"div\",{className:\"leaderboard-container\",children:sortedData.length>0?sortedData.map((user,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"leaderboard-entry\",onClick:()=>handleViewUserBoard(user.display_name),title:\"View \".concat(user.display_name,\"'s board\"),children:[/*#__PURE__*/_jsx(\"div\",{className:\"user-rank\",children:index+1}),/*#__PURE__*/_jsx(\"div\",{className:\"user-name\",title:user.display_name// Show full name on hover\n,children:truncateDisplayName(user.display_name)}),/*#__PURE__*/_jsx(\"div\",{className:\"score-number\",children:user.score})]},index)):/*#__PURE__*/_jsx(\"div\",{className:\"no-data-message\",children:\"No scores yet. Be the first to score!\"})})]});};export default Leaderboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "jsx", "_jsx", "jsxs", "_jsxs", "Leaderboard", "navigate", "leaderboardData", "setLeaderboardData", "loading", "setLoading", "error", "setError", "fetchLeaderboard", "response", "fetch", "ok", "Error", "concat", "status", "data", "json", "err", "console", "intervalId", "setInterval", "clearInterval", "sortedData", "sort", "a", "b", "score", "maxScore", "Math", "max", "map", "user", "truncateDisplayName", "displayName", "length", "substring", "handleViewUserBoard", "encodeURIComponent", "className", "children", "index", "onClick", "display_name", "title"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Board/Leaderboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst Leaderboard = () => {\r\n  const navigate = useNavigate();\r\n  // State for leaderboard data\r\n  const [leaderboardData, setLeaderboardData] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  // Fetch leaderboard data\r\n  useEffect(() => {\r\n    const fetchLeaderboard = async () => {\r\n      try {\r\n        setLoading(true);\r\n\r\n        const response = await fetch('http://localhost:5000/api/leaderboard');\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP error! Status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        setLeaderboardData(data);\r\n      } catch (err) {\r\n        console.error('Error fetching leaderboard:', err);\r\n        setError('Failed to load leaderboard data');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchLeaderboard();\r\n\r\n    // Set up polling to refresh leaderboard data\r\n    const intervalId = setInterval(fetchLeaderboard, 30000); // Refresh every 30 seconds\r\n\r\n    return () => clearInterval(intervalId); // Clean up on unmount\r\n  }, []);\r\n\r\n  // Sort data by score in descending order\r\n  const sortedData = [...leaderboardData].sort((a, b) => b.score - a.score);\r\n\r\n  // Find the maximum score for scaling\r\n  const maxScore = Math.max(...sortedData.map(user => user.score || 0), 1); // Ensure we don't divide by zero\r\n\r\n  // Truncate display name to max 16 characters\r\n  const truncateDisplayName = (displayName) => {\r\n    if (displayName.length <= 16) {\r\n      return displayName;\r\n    }\r\n    return displayName.substring(0, 16) + '...';\r\n  };\r\n\r\n  // Handle clicking on a leaderboard entry to view their board\r\n  const handleViewUserBoard = (displayName) => {\r\n    navigate(`/boards/${encodeURIComponent(displayName)}`);\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"leaderboard-section\">\r\n        <h2>Leaderboard</h2>\r\n        <div className=\"loading-message\">Loading leaderboard...</div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"leaderboard-section\">\r\n        <h2>Leaderboard</h2>\r\n        <div className=\"error-message\">{error}</div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"leaderboard-section\">\r\n      <h2>Leaderboard</h2>\r\n      <div className=\"leaderboard-container\">\r\n        {sortedData.length > 0 ? (\r\n          sortedData.map((user, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"leaderboard-entry\"\r\n              onClick={() => handleViewUserBoard(user.display_name)}\r\n              title={`View ${user.display_name}'s board`}\r\n            >\r\n              <div className=\"user-rank\">{index + 1}</div>\r\n              <div\r\n                className=\"user-name\"\r\n                title={user.display_name} // Show full name on hover\r\n              >\r\n                {truncateDisplayName(user.display_name)}\r\n              </div>\r\n              <div className=\"score-number\">{user.score}</div>\r\n            </div>\r\n          ))\r\n        ) : (\r\n          <div className=\"no-data-message\">No scores yet. Be the first to score!</div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Leaderboard;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,QAAQ,CAAGN,WAAW,CAAC,CAAC,CAC9B;AACA,KAAM,CAACO,eAAe,CAAEC,kBAAkB,CAAC,CAAGV,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACW,OAAO,CAAEC,UAAU,CAAC,CAAGZ,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACa,KAAK,CAAEC,QAAQ,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CAExC;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAc,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACFH,UAAU,CAAC,IAAI,CAAC,CAEhB,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,uCAAuC,CAAC,CAErE,GAAI,CAACD,QAAQ,CAACE,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAC,KAAK,wBAAAC,MAAA,CAAwBJ,QAAQ,CAACK,MAAM,CAAE,CAAC,CAC3D,CAEA,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAClCb,kBAAkB,CAACY,IAAI,CAAC,CAC1B,CAAE,MAAOE,GAAG,CAAE,CACZC,OAAO,CAACZ,KAAK,CAAC,6BAA6B,CAAEW,GAAG,CAAC,CACjDV,QAAQ,CAAC,iCAAiC,CAAC,CAC7C,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDG,gBAAgB,CAAC,CAAC,CAElB;AACA,KAAM,CAAAW,UAAU,CAAGC,WAAW,CAACZ,gBAAgB,CAAE,KAAK,CAAC,CAAE;AAEzD,MAAO,IAAMa,aAAa,CAACF,UAAU,CAAC,CAAE;AAC1C,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAG,UAAU,CAAG,CAAC,GAAGpB,eAAe,CAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACC,KAAK,CAAGF,CAAC,CAACE,KAAK,CAAC,CAEzE;AACA,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,UAAU,CAACQ,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACL,KAAK,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE;AAE1E;AACA,KAAM,CAAAM,mBAAmB,CAAIC,WAAW,EAAK,CAC3C,GAAIA,WAAW,CAACC,MAAM,EAAI,EAAE,CAAE,CAC5B,MAAO,CAAAD,WAAW,CACpB,CACA,MAAO,CAAAA,WAAW,CAACE,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAAK,CAC7C,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAIH,WAAW,EAAK,CAC3ChC,QAAQ,YAAAY,MAAA,CAAYwB,kBAAkB,CAACJ,WAAW,CAAC,CAAE,CAAC,CACxD,CAAC,CAED,GAAI7B,OAAO,CAAE,CACX,mBACEL,KAAA,QAAKuC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC1C,IAAA,OAAA0C,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB1C,IAAA,QAAKyC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,wBAAsB,CAAK,CAAC,EAC1D,CAAC,CAEV,CAEA,GAAIjC,KAAK,CAAE,CACT,mBACEP,KAAA,QAAKuC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC1C,IAAA,OAAA0C,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB1C,IAAA,QAAKyC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEjC,KAAK,CAAM,CAAC,EACzC,CAAC,CAEV,CAEA,mBACEP,KAAA,QAAKuC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC1C,IAAA,OAAA0C,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB1C,IAAA,QAAKyC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnCjB,UAAU,CAACY,MAAM,CAAG,CAAC,CACpBZ,UAAU,CAACQ,GAAG,CAAC,CAACC,IAAI,CAAES,KAAK,gBACzBzC,KAAA,QAEEuC,SAAS,CAAC,mBAAmB,CAC7BG,OAAO,CAAEA,CAAA,GAAML,mBAAmB,CAACL,IAAI,CAACW,YAAY,CAAE,CACtDC,KAAK,SAAA9B,MAAA,CAAUkB,IAAI,CAACW,YAAY,YAAW,CAAAH,QAAA,eAE3C1C,IAAA,QAAKyC,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEC,KAAK,CAAG,CAAC,CAAM,CAAC,cAC5C3C,IAAA,QACEyC,SAAS,CAAC,WAAW,CACrBK,KAAK,CAAEZ,IAAI,CAACW,YAAc;AAAA,CAAAH,QAAA,CAEzBP,mBAAmB,CAACD,IAAI,CAACW,YAAY,CAAC,CACpC,CAAC,cACN7C,IAAA,QAAKyC,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAER,IAAI,CAACL,KAAK,CAAM,CAAC,GAZ3Cc,KAaF,CACN,CAAC,cAEF3C,IAAA,QAAKyC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,uCAAqC,CAAK,CAC5E,CACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}